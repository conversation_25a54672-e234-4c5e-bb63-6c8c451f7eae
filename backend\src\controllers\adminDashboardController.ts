import { Response } from 'express';
import User from '../models/User';
import City from '../models/City';
import Place from '../models/Place';
import Review from '../models/Review';
import { asyncHandler } from '../middleware/errorHandler';
import { AdminAuthRequest } from './adminAuthController';

// @desc    Get dashboard statistics
// @route   GET /api/admin/dashboard/stats
// @access  Private (Admin)
export const getDashboardStats = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  // Get total counts
  const totalUsers = await User.countDocuments();
  const totalCities = await City.countDocuments({ isPublished: true });
  const totalPlaces = await Place.countDocuments({ isPublished: true });
  const totalReviews = await Review.countDocuments();
  const pendingReviews = await Review.countDocuments({ isApproved: false });

  // Get recent counts (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const recentUsers = await User.countDocuments({ 
    createdAt: { $gte: thirtyDaysAgo } 
  });
  
  const recentCities = await City.countDocuments({ 
    createdAt: { $gte: thirtyDaysAgo },
    isPublished: true
  });
  
  const recentPlaces = await Place.countDocuments({ 
    createdAt: { $gte: thirtyDaysAgo },
    isPublished: true
  });

  // Calculate average rating
  const ratingStats = await Place.aggregate([
    {
      $match: {
        averageRating: { $gt: 0 }
      }
    },
    {
      $group: {
        _id: null,
        averageRating: { $avg: '$averageRating' }
      }
    }
  ]);

  const averageRating = ratingStats.length > 0 ? ratingStats[0].averageRating : 0;

  res.json({
    success: true,
    data: {
      totalUsers,
      totalCities,
      totalPlaces,
      totalReviews,
      pendingReviews,
      recentUsers,
      recentCities,
      recentPlaces,
      averageRating: Math.round(averageRating * 10) / 10
    }
  });
});

// @desc    Get recent activity
// @route   GET /api/admin/dashboard/activity
// @access  Private (Admin)
export const getRecentActivity = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const limit = parseInt(req.query.limit as string) || 10;

  // Get recent users
  const recentUsers = await User.find()
    .sort({ createdAt: -1 })
    .limit(3)
    .select('name email createdAt');

  // Get recent cities
  const recentCities = await City.find({ isPublished: true })
    .sort({ createdAt: -1 })
    .limit(3)
    .select('name country createdAt');

  // Get recent places
  const recentPlaces = await Place.find({ isPublished: true })
    .sort({ createdAt: -1 })
    .limit(3)
    .select('name category createdAt')
    .populate('city', 'name');

  // Get recent reviews
  const recentReviews = await Review.find()
    .sort({ createdAt: -1 })
    .limit(3)
    .select('rating comment entityType createdAt')
    .populate('user', 'name')
    .populate('entityId', 'name');

  // Combine and format activities
  const activities: any[] = [];

  recentUsers.forEach(user => {
    activities.push({
      id: `user_${user._id}`,
      type: 'user_registered',
      description: `New user registered: ${user.name} (${user.email})`,
      timestamp: user.createdAt,
      data: user
    });
  });

  recentCities.forEach(city => {
    activities.push({
      id: `city_${city._id}`,
      type: 'city_added',
      description: `New city added: ${city.name}, ${city.country}`,
      timestamp: city.createdAt,
      data: city
    });
  });

  recentPlaces.forEach(place => {
    activities.push({
      id: `place_${place._id}`,
      type: 'place_added',
      description: `New place added: ${place.name} (${place.category})`,
      timestamp: place.createdAt,
      data: place
    });
  });

  recentReviews.forEach(review => {
    const entityName = review.entityId?.name || 'Unknown';
    activities.push({
      id: `review_${review._id}`,
      type: 'review_submitted',
      description: `New review submitted for ${entityName} (${review.rating} stars)`,
      timestamp: review.createdAt,
      data: review
    });
  });

  // Sort by timestamp and limit
  activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  const limitedActivities = activities.slice(0, limit);

  res.json({
    success: true,
    data: limitedActivities
  });
});

// @desc    Get analytics data
// @route   GET /api/admin/dashboard/analytics
// @access  Private (Admin)
export const getAnalytics = asyncHandler(async (req: AdminAuthRequest, res: Response) => {
  const days = parseInt(req.query.days as string) || 30;
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  // User growth over time
  const userGrowth = await User.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
    }
  ]);

  // Content creation over time
  const contentGrowth = await Place.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate },
        isPublished: true
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$createdAt' },
          month: { $month: '$createdAt' },
          day: { $dayOfMonth: '$createdAt' }
        },
        count: { $sum: 1 }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
    }
  ]);

  // Places by category
  const placesByCategory = await Place.aggregate([
    {
      $match: { isPublished: true }
    },
    {
      $group: {
        _id: '$category',
        count: { $sum: 1 }
      }
    },
    {
      $sort: { count: -1 }
    }
  ]);

  // Cities by country
  const citiesByCountry = await City.aggregate([
    {
      $match: { isPublished: true }
    },
    {
      $group: {
        _id: '$country',
        count: { $sum: 1 }
      }
    },
    {
      $sort: { count: -1 }
    },
    {
      $limit: 10
    }
  ]);

  res.json({
    success: true,
    data: {
      userGrowth,
      contentGrowth,
      placesByCategory,
      citiesByCountry
    }
  });
});
