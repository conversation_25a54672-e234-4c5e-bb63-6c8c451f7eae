import express from 'express';
import { body, query, param } from 'express-validator';

// Admin Auth Controllers
import {
  adminLogin,
  getAdminProfile,
  updateAdminProfile,
  changeAdminPassword,
  adminLogout
} from '../controllers/adminAuthController';

// Admin User Management Controllers
import {
  getUsers,
  getUser,
  updateUser,
  deleteUser,
  toggleUserStatus,
  bulkDeleteUsers,
  getUserStats
} from '../controllers/adminUserController';

// Admin Dashboard Controllers
import {
  getDashboardStats,
  getRecentActivity,
  getAnalytics
} from '../controllers/adminDashboardController';

// Admin Middleware
import { protectAdmin, requireAdmin, requireSuperAdmin } from '../middleware/adminAuth';

const router = express.Router();

// ============================================================================
// ADMIN AUTHENTICATION ROUTES
// ============================================================================

// @route   POST /api/admin/auth/login
// @desc    Admin login
// @access  Public
router.post('/auth/login', [
  body('email')
    .isEmail()
    .withMessage('Please provide a valid email'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters')
], adminLogin);

// @route   GET /api/admin/auth/me
// @desc    Get current admin profile
// @access  Private (Admin)
router.get('/auth/me', protectAdmin, getAdminProfile);

// @route   PUT /api/admin/auth/profile
// @desc    Update admin profile
// @access  Private (Admin)
router.put('/auth/profile', [
  protectAdmin,
  body('name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email')
], updateAdminProfile);

// @route   PUT /api/admin/auth/password
// @desc    Change admin password
// @access  Private (Admin)
router.put('/auth/password', [
  protectAdmin,
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters')
], changeAdminPassword);

// @route   POST /api/admin/auth/logout
// @desc    Admin logout
// @access  Private (Admin)
router.post('/auth/logout', protectAdmin, adminLogout);

// ============================================================================
// ADMIN DASHBOARD ROUTES
// ============================================================================

// @route   GET /api/admin/dashboard/stats
// @desc    Get dashboard statistics
// @access  Private (Admin)
router.get('/dashboard/stats', protectAdmin, getDashboardStats);

// @route   GET /api/admin/dashboard/activity
// @desc    Get recent activity
// @access  Private (Admin)
router.get('/dashboard/activity', [
  protectAdmin,
  query('limit')
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage('Limit must be between 1 and 50')
], getRecentActivity);

// @route   GET /api/admin/dashboard/analytics
// @desc    Get analytics data
// @access  Private (Admin)
router.get('/dashboard/analytics', [
  protectAdmin,
  query('days')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('Days must be between 1 and 365')
], getAnalytics);

// ============================================================================
// ADMIN USER MANAGEMENT ROUTES
// ============================================================================

// @route   GET /api/admin/users/stats
// @desc    Get user statistics
// @access  Private (Admin)
router.get('/users/stats', protectAdmin, getUserStats);

// @route   POST /api/admin/users/bulk-delete
// @desc    Bulk delete users
// @access  Private (Super Admin)
router.post('/users/bulk-delete', [
  protectAdmin,
  requireSuperAdmin,
  body('ids')
    .isArray({ min: 1 })
    .withMessage('Please provide an array of user IDs')
], bulkDeleteUsers);

// @route   GET /api/admin/users
// @desc    Get all users with pagination and filtering
// @access  Private (Admin)
router.get('/users', [
  protectAdmin,
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search term must be between 1 and 100 characters'),
  query('sortBy')
    .optional()
    .isIn(['name', 'email', 'createdAt', 'lastLoginAt', 'role'])
    .withMessage('Invalid sort field'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc')
], getUsers);

// @route   GET /api/admin/users/:id
// @desc    Get single user
// @access  Private (Admin)
router.get('/users/:id', [
  protectAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid user ID')
], getUser);

// @route   PUT /api/admin/users/:id
// @desc    Update user
// @access  Private (Admin)
router.put('/users/:id', [
  protectAdmin,
  requireAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid user ID'),
  body('name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please provide a valid email'),
  body('role')
    .optional()
    .isIn(['user', 'contributor', 'moderator'])
    .withMessage('Invalid role'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive must be a boolean')
], updateUser);

// @route   DELETE /api/admin/users/:id
// @desc    Delete user
// @access  Private (Super Admin)
router.delete('/users/:id', [
  protectAdmin,
  requireSuperAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid user ID')
], deleteUser);

// @route   PATCH /api/admin/users/:id/toggle-status
// @desc    Toggle user active status
// @access  Private (Admin)
router.patch('/users/:id/toggle-status', [
  protectAdmin,
  requireAdmin,
  param('id')
    .isMongoId()
    .withMessage('Invalid user ID')
], toggleUserStatus);

export default router;
